# pyAHC集成HDF5数据同化支持方案

## 1. 项目背景与需求分析

### 1.1 当前项目架构
pyAHC是一个Python封装的AHC（Agricultural Hydrological Crop）模型，具有以下特点：
- 基于Pydantic的组件化架构
- 支持多种输入输出格式（.txt, .csv等）
- 完整的验证框架
- 模块化的组件设计（气象、作物、土壤等）

### 1.2 数据同化需求
您的需求场景：
1. **日循环模拟**：每天运行模型，前一天的输出状态作为下一天的输入
2. **状态变量传递**：土壤含水量、作物LAI等状态变量需要在时间步之间传递
3. **数据同化准备**：为后续集成观测数据同化功能提供基础架构
4. **科学数据管理**：结构化存储模型状态、参数和结果

### 1.3 HDF5的优势
- **层次化存储**：适合存储复杂的模型状态和时间序列数据
- **高性能**：二进制格式，读写效率高
- **跨平台**：广泛的科学计算支持
- **元数据支持**：可存储丰富的描述信息
- **压缩支持**：节省存储空间

## 2. HDF5集成架构设计

### 2.1 核心设计原则
1. **非侵入性**：不破坏现有组件架构
2. **可扩展性**：为未来数据同化功能预留接口
3. **向后兼容**：保持对现有文本格式的支持
4. **性能优化**：高效的数据读写操作

### 2.2 HDF5数据结构设计

```
simulation.h5
├── metadata/                    # 模拟元数据
│   ├── model_info              # 模型版本、配置信息
│   ├── simulation_period       # 模拟时间段
│   └── creation_info           # 创建时间、用户等
├── parameters/                  # 模型参数
│   ├── crop/                   # 作物参数
│   ├── soil/                   # 土壤参数
│   ├── meteorology/            # 气象参数
│   └── management/             # 管理参数
├── states/                      # 状态变量时间序列
│   ├── soil_moisture/          # 土壤含水量 [time, depth]
│   ├── crop_lai/               # 叶面积指数 [time]
│   ├── crop_biomass/           # 生物量 [time]
│   ├── root_depth/             # 根深 [time]
│   └── water_stress/           # 水分胁迫 [time]
├── inputs/                      # 输入数据
│   ├── meteorology/            # 气象数据 [time, variables]
│   ├── irrigation/             # 灌溉数据 [time]
│   └── management/             # 管理措施 [time]
├── outputs/                     # 输出结果
│   ├── daily/                  # 日输出 [time, variables]
│   ├── water_balance/          # 水平衡 [time, components]
│   └── crop_growth/            # 作物生长 [time, variables]
└── assimilation/               # 数据同化相关（预留）
    ├── observations/           # 观测数据
    ├── analysis/               # 分析结果
    └── ensemble/               # 集合成员（如果使用EnKF）
```

### 2.3 类层次结构设计

```python
# 核心HDF5管理类
pyahc.db.hdf5.HDF5Manager
├── StateManager          # 状态变量管理
├── ParameterManager      # 参数管理  
├── ResultManager         # 结果管理
└── AssimilationManager   # 数据同化管理（预留）

# 状态变量类
pyahc.db.hdf5.StateVariable
├── SoilMoistureState     # 土壤含水量状态
├── CropState             # 作物状态（LAI, 生物量等）
├── WaterBalanceState     # 水平衡状态
└── CustomState           # 自定义状态变量

# 数据同化接口（预留）
pyahc.db.hdf5.AssimilationInterface
├── ObservationHandler    # 观测数据处理
├── StateUpdater          # 状态更新器
└── EnsembleManager       # 集合管理
```

## 3. 核心功能实现

### 3.1 HDF5Manager核心类

```python
class HDF5Manager:
    """HDF5数据管理核心类"""
    
    def __init__(self, filepath: Path, mode: str = 'a'):
        self.filepath = filepath
        self.mode = mode
        self._file = None
        
    def initialize_structure(self, model: Model) -> None:
        """初始化HDF5文件结构"""
        
    def save_model_state(self, date: datetime, states: Dict[str, Any]) -> None:
        """保存模型状态"""
        
    def load_model_state(self, date: datetime) -> Dict[str, Any]:
        """加载模型状态"""
        
    def save_model_results(self, date: datetime, results: Result) -> None:
        """保存模型结果"""
        
    def get_state_timeseries(self, variable: str, 
                           start_date: datetime = None,
                           end_date: datetime = None) -> np.ndarray:
        """获取状态变量时间序列"""
```

### 3.2 状态变量提取器

```python
class StateExtractor:
    """从模型结果中提取状态变量"""
    
    @staticmethod
    def extract_soil_moisture(result: Result) -> np.ndarray:
        """提取土壤含水量"""
        
    @staticmethod  
    def extract_crop_lai(result: Result) -> float:
        """提取作物LAI"""
        
    @staticmethod
    def extract_crop_biomass(result: Result) -> float:
        """提取作物生物量"""
```

### 3.3 状态变量注入器

```python
class StateInjector:
    """将状态变量注入到模型输入中"""
    
    @staticmethod
    def inject_soil_moisture(model: Model, moisture: np.ndarray) -> Model:
        """注入土壤含水量"""
        
    @staticmethod
    def inject_crop_state(model: Model, lai: float, biomass: float) -> Model:
        """注入作物状态"""
```

## 4. 数据同化工作流程

### 4.1 初始化阶段
```python
def initialize_simulation(config: Dict) -> HDF5Manager:
    """初始化数据同化模拟"""
    # 1. 创建HDF5文件
    # 2. 初始化数据结构
    # 3. 保存初始参数和状态
    # 4. 返回管理器实例
```

### 4.2 日循环模拟
```python
def daily_simulation_step(hdf5_manager: HDF5Manager, 
                         current_date: datetime,
                         model_template: Model) -> Result:
    """单日模拟步骤"""
    # 1. 从HDF5加载前一日状态（如果存在）
    # 2. 更新模型输入状态
    # 3. 运行模型
    # 4. 提取输出状态
    # 5. 保存状态和结果到HDF5
    # 6. 返回结果
```

### 4.3 数据同化接口（预留）
```python
def assimilate_observations(hdf5_manager: HDF5Manager,
                          observations: Dict[str, Any],
                          date: datetime) -> None:
    """数据同化接口（预留实现）"""
    # 1. 加载模型状态
    # 2. 应用同化算法（EnKF等）
    # 3. 更新状态
    # 4. 保存同化结果
```

## 5. 集成到现有架构

### 5.1 Model类扩展
```python
# 在pyahc/model/model.py中添加HDF5支持
class Model:
    # 现有代码...
    
    def save_to_hdf5(self, hdf5_manager: HDF5Manager, date: datetime) -> None:
        """保存模型到HDF5"""
        
    @classmethod
    def load_from_hdf5(cls, hdf5_manager: HDF5Manager, date: datetime) -> 'Model':
        """从HDF5加载模型"""
```

### 5.2 Result类扩展
```python
# 在pyahc/model/result.py中添加状态提取方法
class Result:
    # 现有代码...
    
    def extract_states(self) -> Dict[str, Any]:
        """提取状态变量"""
        
    def get_soil_moisture_profile(self) -> np.ndarray:
        """获取土壤含水量剖面"""
```

### 5.3 组件级别的状态支持
```python
# 为关键组件添加状态序列化/反序列化方法
class SoilMoisture:
    def to_state_dict(self) -> Dict[str, Any]:
        """转换为状态字典"""
        
    @classmethod
    def from_state_dict(cls, state: Dict[str, Any]) -> 'SoilMoisture':
        """从状态字典创建"""
```

## 6. 使用示例

### 6.1 基本使用流程
```python
from pyahc.db.hdf5 import HDF5Manager
from pyahc.model.model import Model
import datetime

# 1. 初始化HDF5管理器
hdf5_manager = HDF5Manager("simulation.h5")

# 2. 创建基础模型
base_model = create_model_components()  # 使用现有函数

# 3. 初始化HDF5结构
hdf5_manager.initialize_structure(base_model)

# 4. 日循环模拟
start_date = datetime.date(2013, 5, 1)
end_date = datetime.date(2013, 9, 25)

current_date = start_date
while current_date <= end_date:
    # 运行单日模拟
    result = daily_simulation_step(hdf5_manager, current_date, base_model)
    
    # 可选：检查是否有观测数据需要同化
    if has_observations(current_date):
        observations = load_observations(current_date)
        assimilate_observations(hdf5_manager, observations, current_date)
    
    current_date += datetime.timedelta(days=1)

# 5. 分析结果
lai_timeseries = hdf5_manager.get_state_timeseries('crop_lai')
soil_moisture_timeseries = hdf5_manager.get_state_timeseries('soil_moisture')
```

### 6.2 状态变量分析
```python
# 获取特定时间段的状态演变
moisture_evolution = hdf5_manager.get_state_timeseries(
    'soil_moisture', 
    start_date=datetime.date(2013, 6, 1),
    end_date=datetime.date(2013, 8, 31)
)

# 可视化状态变量
import matplotlib.pyplot as plt
plt.figure(figsize=(12, 6))
plt.subplot(1, 2, 1)
plt.plot(lai_timeseries)
plt.title('Crop LAI Evolution')
plt.subplot(1, 2, 2)
plt.imshow(moisture_evolution.T, aspect='auto')
plt.title('Soil Moisture Profile')
plt.show()
```

## 7. 实施计划

### 7.1 第一阶段：基础HDF5支持
- [ ] 实现HDF5Manager核心类
- [ ] 实现StateExtractor和StateInjector
- [ ] 添加基本的状态变量支持
- [ ] 集成到现有Model和Result类

### 7.2 第二阶段：完整工作流程
- [ ] 实现日循环模拟工作流程
- [ ] 添加状态变量时间序列管理
- [ ] 实现数据验证和错误处理
- [ ] 编写完整的使用文档

### 7.3 第三阶段：数据同化准备
- [ ] 设计数据同化接口
- [ ] 实现观测数据处理
- [ ] 预留集合模拟支持
- [ ] 性能优化和测试

## 8. 技术优势

1. **科学性**：基于HDF5的层次化数据结构，符合科学计算标准
2. **可扩展性**：模块化设计，易于添加新的状态变量和功能
3. **性能**：高效的二进制存储，支持大规模时间序列数据
4. **兼容性**：不破坏现有架构，保持向后兼容
5. **标准化**：遵循科学数据管理最佳实践

这个方案为pyAHC项目提供了坚实的数据同化基础架构，既满足当前的日循环模拟需求，又为未来的数据同化功能扩展奠定了基础。
