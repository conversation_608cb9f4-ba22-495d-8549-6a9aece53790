# pyAHC集成HDF5数据同化支持方案

## 1. 项目背景与需求分析

### 1.1 当前项目架构
pyAHC是一个Python封装的AHC（Agricultural Hydrological Crop）模型，具有以下特点：
- 基于Pydantic的组件化架构
- 支持多种输入输出格式（.txt, .csv等）
- 完整的验证框架
- 模块化的组件设计（气象、作物、土壤等）

### 1.2 数据同化需求
您的需求场景：
1. **日循环模拟**：每天运行模型，前一天的输出状态作为下一天的输入
2. **状态变量传递**：土壤含水量、作物LAI等状态变量需要在时间步之间传递
3. **数据同化准备**：为后续集成观测数据同化功能提供基础架构
4. **科学数据管理**：结构化存储模型状态、参数和结果

### 1.3 HDF5的优势
- **层次化存储**：适合存储复杂的模型状态和时间序列数据
- **高性能**：二进制格式，读写效率高
- **跨平台**：广泛的科学计算支持
- **元数据支持**：可存储丰富的描述信息
- **压缩支持**：节省存储空间

## 2. HDF5集成架构设计

### 2.1 核心设计原则
1. **非侵入性**：不破坏现有组件架构
2. **可扩展性**：为未来数据同化功能预留接口
3. **向后兼容**：保持对现有文本格式的支持
4. **性能优化**：高效的数据读写操作

### 2.2 HDF5数据结构设计

基于您的建议，采用更加清晰的项目-日期分层结构：

```
project.h5
├── corn_001-2013/                    # 项目组（玉米地块001-2013年）
│   ├── metadata/                     # 项目元数据
│   │   ├── model_info               # 模型版本、配置信息
│   │   ├── simulation_period        # 模拟时间段
│   │   ├── location_info            # 地块位置信息
│   │   └── crop_info                # 作物信息
│   ├── day_05-01/                    # 日期组（5月1日）
│   │   ├── input/                    # 模型输入
│   │   │   └── model_object         # pickled Model对象
│   │   ├── output/                   # 模型输出
│   │   │   └── result_object        # pickled Result对象
│   │   └── state-parameter_variables/ # 状态-参数变量
│   │       ├── soil_moisture         # 土壤含水量（状态变量）
│   │       ├── lai                   # 叶面积指数（状态变量）
│   │       ├── biomass               # 生物量（状态变量）
│   │       ├── root_depth            # 根深（状态变量）
│   │       ├── groundwater_level     # 地下水位（状态变量）
│   │       ├── param_hydraulic_conductivity # 土壤导水率（参数变量）
│   │       ├── param_crop_coefficient       # 作物系数（参数变量）
│   │       └── assimilated_*         # 数据同化后的变量
│   ├── day_05-02/                    # 5月2日
│   │   ├── input/                    # 包含前一天的状态-参数变量
│   │   ├── output/                   # 当天模拟结果
│   │   └── state-parameter_variables/ # 状态-参数变量
│   ├── day_05-03/                    # 5月3日
│   │   └── ...                       # 类似结构
│   └── summary/                      # 项目汇总数据
│       ├── timeseries/              # 时间序列汇总
│       │   ├── lai_timeseries       # LAI时间序列
│       │   ├── biomass_timeseries   # 生物量时间序列
│       │   └── moisture_timeseries  # 土壤含水量时间序列
│       └── statistics/              # 统计信息
├── corn_002-2013/                    # 其他地块项目
│   └── ...                          # 相同结构
└── wheat_001-2014/                   # 不同作物项目
    └── ...                          # 相同结构
```

#### 数据结构优势：
1. **项目隔离**：不同地块和年份的数据完全分离
2. **日期组织**：每日数据独立存储，便于访问和管理
3. **状态-参数分离**：清晰区分状态变量和参数变量
4. **对象序列化**：直接存储Model和Result对象，保持完整性
5. **汇总支持**：项目级别的时间序列和统计数据

### 2.3 类层次结构设计

基于新的分层结构，重新设计类层次：

```python
# 核心HDF5管理类
pyahc.db.hdf5.ProjectHDF5Manager
├── ProjectManager        # 项目级别管理
├── DailyManager         # 日期级别管理
├── StateParameterManager # 状态-参数变量管理
└── AssimilationManager   # 数据同化管理（预留）

# 项目管理类
pyahc.db.hdf5.ProjectManager
├── create_project()      # 创建新项目
├── list_projects()       # 列出所有项目
├── get_project_metadata() # 获取项目元数据
└── delete_project()      # 删除项目

# 日期管理类
pyahc.db.hdf5.DailyManager
├── save_daily_data()     # 保存日数据
├── load_daily_data()     # 加载日数据
├── get_date_range()      # 获取日期范围
└── copy_states_to_next_day() # 状态传递到下一天

# 状态-参数管理类
pyahc.db.hdf5.StateParameterManager
├── StateVariables        # 状态变量子类
│   ├── soil_moisture     # 土壤含水量
│   ├── lai              # 叶面积指数
│   ├── biomass          # 生物量
│   ├── root_depth       # 根深
│   └── groundwater_level # 地下水位
├── ParameterVariables    # 参数变量子类
│   ├── hydraulic_conductivity # 土壤导水率
│   ├── crop_coefficient      # 作物系数
│   └── custom_parameters     # 自定义参数
└── AssimilatedVariables  # 同化变量子类
    ├── assimilated_lai   # 同化后LAI
    ├── assimilated_biomass # 同化后生物量
    └── assimilated_moisture # 同化后土壤含水量

# 对象序列化管理
pyahc.db.hdf5.ObjectManager
├── save_model_object()   # 保存Model对象
├── load_model_object()   # 加载Model对象
├── save_result_object()  # 保存Result对象
└── load_result_object()  # 加载Result对象
```

## 3. 核心功能实现

### 3.1 ProjectHDF5Manager核心类

```python
class ProjectHDF5Manager:
    """基于项目-日期结构的HDF5数据管理核心类"""

    def __init__(self, filepath: Path, mode: str = 'a'):
        self.filepath = filepath
        self.mode = mode
        self._file = None
        self.logger = logging.getLogger(__name__)

    def __enter__(self):
        self.open()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def open(self):
        """打开HDF5文件"""
        if self._file is None:
            self._file = h5py.File(self.filepath, self.mode)
            self.logger.info(f"Opened HDF5 file: {self.filepath}")

    def close(self):
        """关闭HDF5文件"""
        if self._file is not None:
            self._file.close()
            self._file = None
            self.logger.info("Closed HDF5 file")

    def create_project(self, project_name: str, metadata: Dict[str, Any]) -> None:
        """创建新项目"""
        self.open()

        if project_name in self._file:
            raise ValueError(f"Project {project_name} already exists")

        # 创建项目组
        project_group = self._file.create_group(project_name)

        # 创建元数据组
        metadata_group = project_group.create_group('metadata')

        # 保存元数据
        for key, value in metadata.items():
            if isinstance(value, dict):
                subgroup = metadata_group.create_group(key)
                for subkey, subvalue in value.items():
                    subgroup.attrs[subkey] = subvalue
            else:
                metadata_group.attrs[key] = value

        self.logger.info(f"Created project: {project_name}")

    def save_daily_data(self, project_name: str, date: Union[date, datetime],
                       model: 'Model', result: 'Result',
                       states: Dict[str, Any], parameters: Dict[str, Any] = None) -> None:
        """保存日数据（模型、结果、状态、参数）"""
        self.open()

        if project_name not in self._file:
            raise ValueError(f"Project {project_name} does not exist")

        # 格式化日期字符串
        if isinstance(date, datetime):
            date = date.date()
        date_str = f"day_{date.strftime('%m-%d')}"

        project_group = self._file[project_name]

        # 创建或获取日期组
        if date_str in project_group:
            day_group = project_group[date_str]
        else:
            day_group = project_group.create_group(date_str)

        # 保存模型输入对象
        self._save_model_object(day_group, model)

        # 保存模型输出对象
        self._save_result_object(day_group, result)

        # 保存状态-参数变量
        self._save_state_parameter_variables(day_group, states, parameters)

        self.logger.info(f"Saved daily data for {project_name}/{date_str}")

    def load_daily_data(self, project_name: str, date: Union[date, datetime]) -> Dict[str, Any]:
        """加载日数据"""
        self.open()

        if isinstance(date, datetime):
            date = date.date()
        date_str = f"day_{date.strftime('%m-%d')}"

        if project_name not in self._file:
            raise ValueError(f"Project {project_name} does not exist")

        project_group = self._file[project_name]
        if date_str not in project_group:
            raise ValueError(f"Date {date_str} not found in project {project_name}")

        day_group = project_group[date_str]

        # 加载各种数据
        data = {
            'model': self._load_model_object(day_group),
            'result': self._load_result_object(day_group),
            'states': self._load_state_variables(day_group),
            'parameters': self._load_parameter_variables(day_group)
        }

        return data
```

    def _save_model_object(self, day_group: h5py.Group, model: 'Model') -> None:
        """保存Model对象"""
        import pickle

        # 创建input组
        if 'input' not in day_group:
            input_group = day_group.create_group('input')
        else:
            input_group = day_group['input']

        # 序列化Model对象
        model_bytes = pickle.dumps(model)

        # 保存为数据集
        if 'model_object' in input_group:
            del input_group['model_object']
        dataset = input_group.create_dataset('model_object', data=np.void(model_bytes))
        dataset.attrs['type'] = 'pickled_model'
        dataset.attrs['class'] = 'pyahc.model.model.Model'

    def _save_result_object(self, day_group: h5py.Group, result: 'Result') -> None:
        """保存Result对象"""
        import pickle

        # 创建output组
        if 'output' not in day_group:
            output_group = day_group.create_group('output')
        else:
            output_group = day_group['output']

        # 序列化Result对象
        result_bytes = pickle.dumps(result)

        # 保存为数据集
        if 'result_object' in output_group:
            del output_group['result_object']
        dataset = output_group.create_dataset('result_object', data=np.void(result_bytes))
        dataset.attrs['type'] = 'pickled_result'
        dataset.attrs['class'] = 'pyahc.model.result.Result'

    def _save_state_parameter_variables(self, day_group: h5py.Group,
                                      states: Dict[str, Any],
                                      parameters: Dict[str, Any] = None) -> None:
        """保存状态-参数变量"""

        # 创建state-parameter_variables组
        if 'state-parameter_variables' not in day_group:
            sp_group = day_group.create_group('state-parameter_variables')
        else:
            sp_group = day_group['state-parameter_variables']

        # 保存状态变量
        for var_name, value in states.items():
            if var_name in sp_group:
                del sp_group[var_name]

            if isinstance(value, np.ndarray):
                dataset = sp_group.create_dataset(var_name, data=value, compression='gzip')
            else:
                dataset = sp_group.create_dataset(var_name, data=value)

            dataset.attrs['type'] = 'state_variable'
            dataset.attrs['units'] = self._get_variable_units(var_name)

        # 保存参数变量
        if parameters:
            for param_name, value in parameters.items():
                param_key = f"param_{param_name}"
                if param_key in sp_group:
                    del sp_group[param_key]

                if isinstance(value, np.ndarray):
                    dataset = sp_group.create_dataset(param_key, data=value, compression='gzip')
                else:
                    dataset = sp_group.create_dataset(param_key, data=value)

                dataset.attrs['type'] = 'parameter_variable'
                dataset.attrs['units'] = self._get_variable_units(param_name)

    def _load_model_object(self, day_group: h5py.Group) -> 'Model':
        """加载Model对象"""
        import pickle

        if 'input' not in day_group or 'model_object' not in day_group['input']:
            return None

        dataset = day_group['input']['model_object']
        model_bytes = dataset[()].tobytes()
        model = pickle.loads(model_bytes)

        return model

    def _load_result_object(self, day_group: h5py.Group) -> 'Result':
        """加载Result对象"""
        import pickle

        if 'output' not in day_group or 'result_object' not in day_group['output']:
            return None

        dataset = day_group['output']['result_object']
        result_bytes = dataset[()].tobytes()
        result = pickle.loads(result_bytes)

        return result

    def _load_state_variables(self, day_group: h5py.Group) -> Dict[str, Any]:
        """加载状态变量"""
        states = {}

        if 'state-parameter_variables' not in day_group:
            return states

        sp_group = day_group['state-parameter_variables']

        for key in sp_group.keys():
            dataset = sp_group[key]
            if dataset.attrs.get('type') == 'state_variable':
                states[key] = dataset[()]

        return states

    def _load_parameter_variables(self, day_group: h5py.Group) -> Dict[str, Any]:
        """加载参数变量"""
        parameters = {}

        if 'state-parameter_variables' not in day_group:
            return parameters

        sp_group = day_group['state-parameter_variables']

        for key in sp_group.keys():
            if key.startswith('param_'):
                dataset = sp_group[key]
                if dataset.attrs.get('type') == 'parameter_variable':
                    param_name = key[6:]  # 移除'param_'前缀
                    parameters[param_name] = dataset[()]

        return parameters

### 3.2 状态变量提取器（适配新结构）

```python
class StateExtractor:
    """从模型结果中提取状态变量 - 适配新的HDF5结构"""

    @staticmethod
    def extract_all_states(result: 'Result') -> Dict[str, Any]:
        """提取所有状态变量"""
        states = {}

        # 提取土壤含水量
        soil_moisture = StateExtractor.extract_soil_moisture(result)
        if soil_moisture is not None:
            states['soil_moisture'] = soil_moisture

        # 提取LAI
        lai = StateExtractor.extract_crop_lai(result)
        if lai is not None:
            states['lai'] = lai

        # 提取生物量
        biomass = StateExtractor.extract_crop_biomass(result)
        if biomass is not None:
            states['biomass'] = biomass

        # 提取根深
        root_depth = StateExtractor.extract_root_depth(result)
        if root_depth is not None:
            states['root_depth'] = root_depth

        # 提取地下水位
        groundwater_level = StateExtractor.extract_groundwater_level(result)
        if groundwater_level is not None:
            states['groundwater_level'] = groundwater_level

        return states

    @staticmethod
    def extract_soil_moisture(result: 'Result') -> Optional[np.ndarray]:
        """提取土壤含水量剖面"""
        try:
            # 从CSV输出中提取
            if hasattr(result, 'csv') and result.csv is not None:
                moisture_cols = [col for col in result.csv.columns
                               if 'moisture' in col.lower() or 'theta' in col.lower()]
                if moisture_cols:
                    return result.csv[moisture_cols].iloc[-1].values

            # 从ASCII输出中解析
            if hasattr(result, 'ascii') and result.ascii:
                for file_ext, content in result.ascii.items():
                    if 'sba' in file_ext.lower():
                        return StateExtractor._parse_soil_moisture_from_sba(content)

            return None

        except Exception as e:
            logging.warning(f"Failed to extract soil moisture: {e}")
            return None
```

    @staticmethod
    def extract_crop_lai(result: 'Result') -> Optional[float]:
        """提取作物叶面积指数"""
        try:
            if hasattr(result, 'csv') and result.csv is not None:
                lai_cols = [col for col in result.csv.columns
                           if 'lai' in col.lower() or 'leaf_area' in col.lower()]
                if lai_cols:
                    return float(result.csv[lai_cols[0]].iloc[-1])

            return None

        except Exception as e:
            logging.warning(f"Failed to extract LAI: {e}")
            return None

    @staticmethod
    def extract_crop_biomass(result: 'Result') -> Optional[float]:
        """提取作物生物量"""
        try:
            if hasattr(result, 'csv') and result.csv is not None:
                biomass_cols = [col for col in result.csv.columns
                              if 'biomass' in col.lower() or 'dry_matter' in col.lower()]
                if biomass_cols:
                    return float(result.csv[biomass_cols[0]].iloc[-1])

            return None

        except Exception as e:
            logging.warning(f"Failed to extract biomass: {e}")
            return None

    @staticmethod
    def extract_root_depth(result: 'Result') -> Optional[float]:
        """提取根深"""
        try:
            if hasattr(result, 'csv') and result.csv is not None:
                root_cols = [col for col in result.csv.columns
                           if 'root' in col.lower() and 'depth' in col.lower()]
                if root_cols:
                    return float(result.csv[root_cols[0]].iloc[-1])

            return None

        except Exception as e:
            logging.warning(f"Failed to extract root depth: {e}")
            return None

    @staticmethod
    def extract_groundwater_level(result: 'Result') -> Optional[float]:
        """提取地下水位"""
        try:
            if hasattr(result, 'csv') and result.csv is not None:
                gw_cols = [col for col in result.csv.columns
                          if 'groundwater' in col.lower() or 'water_table' in col.lower()]
                if gw_cols:
                    return float(result.csv[gw_cols[0]].iloc[-1])

            return None

        except Exception as e:
            logging.warning(f"Failed to extract groundwater level: {e}")
            return None

### 3.3 状态变量注入器（适配新结构）

```python
class StateInjector:
    """将状态变量注入到模型输入中 - 适配新的HDF5结构"""

    @staticmethod
    def inject_all_states(model: 'Model', states: Dict[str, Any]) -> 'Model':
        """注入所有状态变量"""
        updated_model = model.model_copy(deep=True)

        # 注入土壤含水量
        if 'soil_moisture' in states:
            updated_model = StateInjector.inject_soil_moisture(
                updated_model, states['soil_moisture']
            )

        # 注入作物状态
        if 'lai' in states or 'biomass' in states:
            updated_model = StateInjector.inject_crop_state(
                updated_model,
                lai=states.get('lai'),
                biomass=states.get('biomass'),
                root_depth=states.get('root_depth')
            )

        # 注入地下水位
        if 'groundwater_level' in states:
            updated_model = StateInjector.inject_groundwater_level(
                updated_model, states['groundwater_level']
            )

        return updated_model

    @staticmethod
    def inject_soil_moisture(model: 'Model', moisture: np.ndarray) -> 'Model':
        """注入土壤含水量"""
        try:
            updated_model = model.model_copy(deep=True)

            if updated_model.soilmoisture and hasattr(updated_model.soilmoisture, 'thetai'):
                updated_model.soilmoisture.thetai = moisture.tolist()

            return updated_model

        except Exception as e:
            logging.error(f"Failed to inject soil moisture: {e}")
            return model

    @staticmethod
    def inject_crop_state(model: 'Model', lai: Optional[float] = None,
                         biomass: Optional[float] = None,
                         root_depth: Optional[float] = None) -> 'Model':
        """注入作物状态变量"""
        try:
            updated_model = model.model_copy(deep=True)

            # 这里需要根据具体的pyAHC模型结构来实现
            # 可能需要更新作物发育阶段或直接设置状态值

            return updated_model

        except Exception as e:
            logging.error(f"Failed to inject crop state: {e}")
            return model

    @staticmethod
    def inject_groundwater_level(model: 'Model', gw_level: float) -> 'Model':
        """注入地下水位"""
        try:
            updated_model = model.model_copy(deep=True)

            # 更新地下水位相关参数
            if updated_model.bottomboundary:
                # 根据pyAHC的具体结构更新地下水位
                pass

            return updated_model

        except Exception as e:
            logging.error(f"Failed to inject groundwater level: {e}")
            return model
```

## 4. 数据同化工作流程（基于新结构）

### 4.1 初始化阶段
```python
def initialize_project_simulation(config: Dict[str, Any]) -> ProjectHDF5Manager:
    """初始化项目数据同化模拟"""

    # 创建HDF5管理器
    hdf5_path = config.get('hdf5_path', 'project.h5')
    manager = ProjectHDF5Manager(hdf5_path)

    # 项目配置
    project_name = config['project_name']  # 例如: "corn_001-2013"

    # 项目元数据
    metadata = {
        'model_info': {
            'pyahc_version': '0.1.0',
            'ahc_version': 'V201',
            'creation_date': datetime.now().isoformat()
        },
        'simulation_period': {
            'start_date': config['start_date'].isoformat(),
            'end_date': config['end_date'].isoformat()
        },
        'location_info': {
            'field_id': config.get('field_id', 'unknown'),
            'latitude': config.get('latitude'),
            'longitude': config.get('longitude'),
            'altitude': config.get('altitude')
        },
        'crop_info': {
            'crop_type': config.get('crop_type', 'corn'),
            'variety': config.get('variety', 'unknown'),
            'planting_date': config.get('planting_date', '').isoformat() if config.get('planting_date') else ''
        }
    }

    # 创建项目
    manager.create_project(project_name, metadata)

    return manager
```

### 4.2 日循环模拟
```python
def daily_simulation_step_v2(manager: ProjectHDF5Manager,
                           project_name: str,
                           current_date: date,
                           base_model: 'Model') -> 'Result':
    """执行单日模拟步骤 - 新版本"""

    # 1. 准备当日模型
    daily_model = base_model.model_copy(deep=True)

    # 2. 加载前一日状态（如果存在）
    previous_date = current_date - timedelta(days=1)
    try:
        previous_data = manager.load_daily_data(project_name, previous_date)
        if previous_data and previous_data['states']:
            daily_model = StateInjector.inject_all_states(
                daily_model, previous_data['states']
            )
            logging.info(f"Loaded states from {previous_date}")
    except Exception as e:
        logging.warning(f"Could not load previous states: {e}")

    # 3. 更新模拟日期
    daily_model.generalsettings.tstart = current_date
    daily_model.generalsettings.tend = current_date

    # 4. 运行模型
    try:
        result = daily_model.run()
        logging.info(f"Model run completed for {current_date}")
    except Exception as e:
        logging.error(f"Model run failed for {current_date}: {e}")
        raise

    # 5. 提取状态变量
    current_states = StateExtractor.extract_all_states(result)

    # 6. 提取参数变量（如果需要）
    current_parameters = extract_parameters_from_model(daily_model)

    # 7. 保存所有数据
    manager.save_daily_data(
        project_name, current_date, daily_model, result,
        current_states, current_parameters
    )

    logging.info(f"Saved all data for {project_name}/{current_date}")

    return result

def extract_parameters_from_model(model: 'Model') -> Dict[str, Any]:
    """从模型中提取参数变量"""
    parameters = {}

    try:
        # 提取土壤导水率
        if model.soilprofile and hasattr(model.soilprofile, 'hydraulic_conductivity'):
            parameters['hydraulic_conductivity'] = model.soilprofile.hydraulic_conductivity

        # 提取作物系数
        if model.crop and hasattr(model.crop, 'crop_coefficient'):
            parameters['crop_coefficient'] = model.crop.crop_coefficient

        # 可以根据需要添加更多参数

    except Exception as e:
        logging.warning(f"Failed to extract parameters: {e}")

    return parameters
```

### 4.3 数据同化接口（预留）
```python
def assimilate_observations_v2(manager: ProjectHDF5Manager,
                             project_name: str,
                             observations: Dict[str, Any],
                             date: date) -> None:
    """数据同化接口 - 新版本（预留实现）"""

    try:
        # 1. 加载当前日数据
        daily_data = manager.load_daily_data(project_name, date)
        current_states = daily_data['states']

        # 2. 应用同化算法（这里是占位符）
        # updated_states = apply_enkf_assimilation(current_states, observations)

        # 3. 保存同化后的状态变量
        with manager:
            date_str = f"day_{date.strftime('%m-%d')}"
            project_group = manager._file[project_name]
            day_group = project_group[date_str]
            sp_group = day_group['state-parameter_variables']

            # 保存同化后的变量
            for var_name, value in observations.items():
                assim_key = f"assimilated_{var_name}"
                if assim_key in sp_group:
                    del sp_group[assim_key]

                dataset = sp_group.create_dataset(assim_key, data=value)
                dataset.attrs['type'] = 'assimilated_variable'
                dataset.attrs['assimilation_date'] = date.isoformat()
                dataset.attrs['original_variable'] = var_name

        logging.info(f"Applied data assimilation for {project_name}/{date}")

    except Exception as e:
        logging.error(f"Data assimilation failed for {project_name}/{date}: {e}")

def run_project_simulation(config: Dict[str, Any]) -> ProjectHDF5Manager:
    """运行完整的项目模拟"""

    # 初始化项目
    manager = initialize_project_simulation(config)
    project_name = config['project_name']

    # 获取模拟参数
    start_date = config['start_date']
    end_date = config['end_date']

    # 创建基础模型
    base_model = create_model_components()  # 使用现有函数

    # 日循环模拟
    current_date = start_date
    total_days = (end_date - start_date).days + 1

    logging.info(f"Starting project simulation: {project_name}")
    logging.info(f"Period: {start_date} to {end_date} ({total_days} days)")

    with manager:
        day_count = 0
        while current_date <= end_date:
            try:
                # 执行单日模拟
                result = daily_simulation_step_v2(
                    manager, project_name, current_date, base_model
                )

                # 可选：检查观测数据同化
                if config.get('enable_assimilation', False):
                    observations = load_observations_for_date(current_date, config)
                    if observations:
                        assimilate_observations_v2(
                            manager, project_name, observations, current_date
                        )

                day_count += 1
                if day_count % 10 == 0:
                    logging.info(f"Completed {day_count}/{total_days} days")

            except Exception as e:
                logging.error(f"Simulation failed on {current_date}: {e}")
                if config.get('stop_on_error', True):
                    raise

            current_date += timedelta(days=1)

        # 生成项目汇总数据
        generate_project_summary(manager, project_name, start_date, end_date)

    logging.info(f"Project simulation completed: {project_name}")
    return manager

def generate_project_summary(manager: ProjectHDF5Manager,
                           project_name: str,
                           start_date: date,
                           end_date: date) -> None:
    """生成项目汇总数据"""

    with manager:
        project_group = manager._file[project_name]

        # 创建summary组
        if 'summary' not in project_group:
            summary_group = project_group.create_group('summary')
            timeseries_group = summary_group.create_group('timeseries')
            statistics_group = summary_group.create_group('statistics')
        else:
            summary_group = project_group['summary']
            timeseries_group = summary_group['timeseries']
            statistics_group = summary_group['statistics']

        # 收集时间序列数据
        lai_series = []
        biomass_series = []
        moisture_series = []
        dates = []

        current_date = start_date
        while current_date <= end_date:
            try:
                daily_data = manager.load_daily_data(project_name, current_date)
                states = daily_data['states']

                dates.append(current_date.isoformat())
                lai_series.append(states.get('lai', np.nan))
                biomass_series.append(states.get('biomass', np.nan))

                if 'soil_moisture' in states:
                    moisture_series.append(states['soil_moisture'])
                else:
                    moisture_series.append(np.full(30, np.nan))  # 假设30层

            except Exception:
                # 如果某天数据不存在，填充NaN
                dates.append(current_date.isoformat())
                lai_series.append(np.nan)
                biomass_series.append(np.nan)
                moisture_series.append(np.full(30, np.nan))

            current_date += timedelta(days=1)

        # 保存时间序列
        if 'lai_timeseries' in timeseries_group:
            del timeseries_group['lai_timeseries']
        lai_dataset = timeseries_group.create_dataset('lai_timeseries', data=lai_series)
        lai_dataset.attrs['units'] = 'm2/m2'
        lai_dataset.attrs['dates'] = [d.encode('utf-8') for d in dates]

        if 'biomass_timeseries' in timeseries_group:
            del timeseries_group['biomass_timeseries']
        biomass_dataset = timeseries_group.create_dataset('biomass_timeseries', data=biomass_series)
        biomass_dataset.attrs['units'] = 'kg/ha'
        biomass_dataset.attrs['dates'] = [d.encode('utf-8') for d in dates]

        if 'moisture_timeseries' in timeseries_group:
            del timeseries_group['moisture_timeseries']
        moisture_dataset = timeseries_group.create_dataset(
            'moisture_timeseries',
            data=np.array(moisture_series),
            compression='gzip'
        )
        moisture_dataset.attrs['units'] = 'cm3/cm3'
        moisture_dataset.attrs['dates'] = [d.encode('utf-8') for d in dates]

        # 计算和保存统计信息
        lai_array = np.array(lai_series)
        biomass_array = np.array(biomass_series)

        stats = {
            'lai_max': np.nanmax(lai_array),
            'lai_mean': np.nanmean(lai_array),
            'lai_min': np.nanmin(lai_array),
            'biomass_max': np.nanmax(biomass_array),
            'biomass_mean': np.nanmean(biomass_array),
            'biomass_final': biomass_array[-1] if not np.isnan(biomass_array[-1]) else np.nan
        }

        for key, value in stats.items():
            statistics_group.attrs[key] = value

        logging.info(f"Generated summary for project {project_name}")
```

## 5. 集成到现有架构（更新版）

### 5.1 Model类扩展
```python
# 在pyahc/model/model.py中添加HDF5支持
class Model:
    # 现有代码...

    def save_to_project_hdf5(self, manager: ProjectHDF5Manager,
                           project_name: str, date: date) -> None:
        """保存模型到项目HDF5"""
        # 提取状态和参数
        states = self.extract_current_states()
        parameters = self.extract_parameters()

        # 运行模型获取结果
        result = self.run()

        # 保存到HDF5
        manager.save_daily_data(project_name, date, self, result, states, parameters)

    @classmethod
    def load_from_project_hdf5(cls, manager: ProjectHDF5Manager,
                             project_name: str, date: date) -> 'Model':
        """从项目HDF5加载模型"""
        daily_data = manager.load_daily_data(project_name, date)
        return daily_data['model']

    def extract_current_states(self) -> Dict[str, Any]:
        """提取当前模型状态"""
        states = {}

        # 提取土壤含水量
        if self.soilmoisture and hasattr(self.soilmoisture, 'thetai'):
            states['soil_moisture'] = np.array(self.soilmoisture.thetai)

        # 可以添加更多状态提取逻辑

        return states

    def extract_parameters(self) -> Dict[str, Any]:
        """提取模型参数"""
        parameters = {}

        # 提取相关参数
        # 这里需要根据具体的pyAHC结构来实现

        return parameters
```

### 5.2 Result类扩展
```python
# 在pyahc/model/result.py中添加状态提取方法
class Result:
    # 现有代码...

    def extract_states_for_hdf5(self) -> Dict[str, Any]:
        """为HDF5存储提取状态变量"""
        return StateExtractor.extract_all_states(self)

    def get_soil_moisture_profile(self) -> Optional[np.ndarray]:
        """获取土壤含水量剖面"""
        return StateExtractor.extract_soil_moisture(self)

    def get_crop_lai(self) -> Optional[float]:
        """获取作物LAI"""
        return StateExtractor.extract_crop_lai(self)

    def get_crop_biomass(self) -> Optional[float]:
        """获取作物生物量"""
        return StateExtractor.extract_crop_biomass(self)
```

### 5.3 组件级别的状态支持
```python
# 为关键组件添加状态序列化/反序列化方法
class SoilMoisture:
    def to_state_dict(self) -> Dict[str, Any]:
        """转换为状态字典"""
        return {
            'thetai': self.thetai if hasattr(self, 'thetai') else None,
            'swinco': self.swinco if hasattr(self, 'swinco') else None
        }

    @classmethod
    def from_state_dict(cls, state: Dict[str, Any]) -> 'SoilMoisture':
        """从状态字典创建"""
        return cls(
            thetai=state.get('thetai'),
            swinco=state.get('swinco', 0)
        )

# 为其他组件添加类似方法
class Crop:
    def to_state_dict(self) -> Dict[str, Any]:
        """转换为状态字典"""
        # 根据具体的作物组件结构实现
        return {}

    @classmethod
    def from_state_dict(cls, state: Dict[str, Any]) -> 'Crop':
        """从状态字典创建"""
        # 根据具体的作物组件结构实现
        return cls()
```

## 6. 使用示例（基于新结构）

### 6.1 基本使用流程
```python
from pyahc.db.hdf5 import ProjectHDF5Manager, StateExtractor, StateInjector
from pyahc.model.model import Model
from datetime import date, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)

def main():
    """主函数 - 演示新HDF5结构的使用"""

    # 1. 配置项目参数
    config = {
        'hdf5_path': 'project.h5',
        'project_name': 'corn_001-2013',
        'start_date': date(2013, 5, 1),
        'end_date': date(2013, 5, 10),  # 短期测试
        'field_id': 'field_001',
        'crop_type': 'corn',
        'latitude': 45.75,
        'longitude': None,
        'altitude': 1039.3,
        'enable_assimilation': False,
        'stop_on_error': True
    }

    # 2. 运行项目模拟
    manager = run_project_simulation(config)

    # 3. 分析结果
    analyze_project_results(manager, config)

def analyze_project_results(manager: ProjectHDF5Manager, config: Dict[str, Any]):
    """分析项目结果"""

    project_name = config['project_name']
    start_date = config['start_date']
    end_date = config['end_date']

    print(f"\n=== 项目结果分析: {project_name} ===")
    print(f"模拟期间: {start_date} 到 {end_date}")

    with manager:
        project_group = manager._file[project_name]

        # 显示项目结构
        print(f"\n项目数据结构:")
        _print_project_structure(project_group, indent=0)

        # 分析汇总数据
        if 'summary' in project_group:
            summary_group = project_group['summary']

            if 'statistics' in summary_group:
                stats = summary_group['statistics']
                print(f"\n统计信息:")
                for key in stats.attrs.keys():
                    print(f"  {key}: {stats.attrs[key]:.3f}")

            if 'timeseries' in summary_group:
                ts_group = summary_group['timeseries']
                if 'lai_timeseries' in ts_group:
                    lai_data = ts_group['lai_timeseries'][:]
                    print(f"\nLAI时间序列:")
                    print(f"  长度: {len(lai_data)} 天")
                    print(f"  范围: {np.nanmin(lai_data):.3f} - {np.nanmax(lai_data):.3f}")

def _print_project_structure(group, indent=0):
    """递归打印项目结构"""
    prefix = "  " * indent

    for key in sorted(group.keys()):
        item = group[key]
        if isinstance(item, h5py.Group):
            print(f"{prefix}{key}/")
            if indent < 2:  # 限制深度
                _print_project_structure(item, indent + 1)
        else:
            shape_str = f" {item.shape}" if hasattr(item, 'shape') else ""
            dtype_str = f" {item.dtype}" if hasattr(item, 'dtype') else ""
            print(f"{prefix}{key}{shape_str}{dtype_str}")

if __name__ == "__main__":
    main()
```

### 6.2 高级使用示例
```python
def advanced_project_usage():
    """高级项目使用示例"""

    # 1. 多项目管理
    def manage_multiple_projects():
        """管理多个项目"""
        manager = ProjectHDF5Manager('multi_project.h5')

        projects = [
            'corn_001-2013', 'corn_002-2013', 'wheat_001-2014'
        ]

        with manager:
            # 列出所有项目
            existing_projects = list(manager._file.keys())
            print(f"现有项目: {existing_projects}")

            # 比较不同项目的结果
            for project in projects:
                if project in manager._file:
                    project_group = manager._file[project]
                    if 'summary/statistics' in project_group:
                        stats = project_group['summary/statistics']
                        max_lai = stats.attrs.get('lai_max', 0)
                        print(f"{project}: 最大LAI = {max_lai:.3f}")

    # 2. 状态变量追踪
    def track_state_evolution():
        """追踪状态变量演化"""
        manager = ProjectHDF5Manager('project.h5')
        project_name = 'corn_001-2013'

        # 获取特定变量的演化
        dates = []
        lai_values = []
        moisture_profiles = []

        start_date = date(2013, 5, 1)
        end_date = date(2013, 5, 31)

        current_date = start_date
        while current_date <= end_date:
            try:
                daily_data = manager.load_daily_data(project_name, current_date)
                states = daily_data['states']

                dates.append(current_date)
                lai_values.append(states.get('lai', np.nan))
                moisture_profiles.append(states.get('soil_moisture', np.full(30, np.nan)))

            except Exception:
                pass

            current_date += timedelta(days=1)

        # 分析演化趋势
        lai_array = np.array(lai_values)
        moisture_array = np.array(moisture_profiles)

        print(f"LAI演化: {np.nanmin(lai_array):.3f} -> {np.nanmax(lai_array):.3f}")
        print(f"平均土壤含水量: {np.nanmean(moisture_array):.3f}")

    # 3. 数据导出
    def export_project_data():
        """导出项目数据"""
        manager = ProjectHDF5Manager('project.h5')
        project_name = 'corn_001-2013'

        with manager:
            project_group = manager._file[project_name]

            # 导出时间序列数据
            if 'summary/timeseries' in project_group:
                ts_group = project_group['summary/timeseries']

                # 导出为CSV
                import pandas as pd

                data = {}
                for var_name in ts_group.keys():
                    dataset = ts_group[var_name]
                    data[var_name] = dataset[:]

                    # 获取日期信息
                    if 'dates' in dataset.attrs:
                        dates = [d.decode('utf-8') for d in dataset.attrs['dates']]
                        data['date'] = dates

                df = pd.DataFrame(data)
                df.to_csv(f'{project_name}_timeseries.csv', index=False)
                print(f"时间序列数据已导出到 {project_name}_timeseries.csv")

    # 执行高级功能
    manage_multiple_projects()
    track_state_evolution()
    export_project_data()
```

### 6.3 数据同化示例（预留）
```python
def data_assimilation_example():
    """数据同化使用示例（预留功能）"""

    manager = ProjectHDF5Manager('project.h5')
    project_name = 'corn_001-2013'

    # 模拟观测数据
    observations = {
        'lai': 2.5,  # 观测到的LAI值
        'biomass': 3500.0,  # 观测到的生物量
        'soil_moisture': np.array([0.25, 0.23, 0.21, 0.20])  # 观测到的土壤含水量
    }

    # 应用数据同化
    assimilation_date = date(2013, 6, 15)
    assimilate_observations_v2(manager, project_name, observations, assimilation_date)

    # 检查同化结果
    with manager:
        date_str = f"day_{assimilation_date.strftime('%m-%d')}"
        project_group = manager._file[project_name]
        day_group = project_group[date_str]
        sp_group = day_group['state-parameter_variables']

        print(f"同化后的变量:")
        for key in sp_group.keys():
            if key.startswith('assimilated_'):
                dataset = sp_group[key]
                print(f"  {key}: {dataset[()]}")
                print(f"    原始变量: {dataset.attrs['original_variable']}")
                print(f"    同化日期: {dataset.attrs['assimilation_date']}")

def visualization_example():
    """可视化示例"""
    import matplotlib.pyplot as plt

    manager = ProjectHDF5Manager('project.h5')
    project_name = 'corn_001-2013'

    with manager:
        project_group = manager._file[project_name]

        if 'summary/timeseries' in project_group:
            ts_group = project_group['summary/timeseries']

            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))

            # LAI时间序列
            if 'lai_timeseries' in ts_group:
                lai_data = ts_group['lai_timeseries'][:]
                dates = [d.decode('utf-8') for d in ts_group['lai_timeseries'].attrs['dates']]
                axes[0, 0].plot(lai_data)
                axes[0, 0].set_title('LAI Evolution')
                axes[0, 0].set_ylabel('LAI (m²/m²)')

            # 生物量时间序列
            if 'biomass_timeseries' in ts_group:
                biomass_data = ts_group['biomass_timeseries'][:]
                axes[0, 1].plot(biomass_data)
                axes[0, 1].set_title('Biomass Evolution')
                axes[0, 1].set_ylabel('Biomass (kg/ha)')

            # 土壤含水量热图
            if 'moisture_timeseries' in ts_group:
                moisture_data = ts_group['moisture_timeseries'][:]
                im = axes[1, 0].imshow(moisture_data.T, aspect='auto', cmap='Blues')
                axes[1, 0].set_title('Soil Moisture Profile')
                axes[1, 0].set_ylabel('Soil Layer')
                axes[1, 0].set_xlabel('Time (days)')
                plt.colorbar(im, ax=axes[1, 0], label='Moisture Content')

            # 统计信息
            if 'statistics' in project_group['summary']:
                stats = project_group['summary']['statistics']
                stats_text = []
                for key in stats.attrs.keys():
                    stats_text.append(f"{key}: {stats.attrs[key]:.3f}")
                axes[1, 1].text(0.1, 0.9, '\n'.join(stats_text),
                               transform=axes[1, 1].transAxes,
                               verticalalignment='top')
                axes[1, 1].set_title('Statistics')
                axes[1, 1].axis('off')

            plt.tight_layout()
            plt.savefig(f'{project_name}_analysis.png', dpi=300, bbox_inches='tight')
            plt.show()
```

## 7. 实施计划（更新版）

### 7.1 第一阶段：基础HDF5支持
- [ ] 实现ProjectHDF5Manager核心类
- [ ] 实现StateExtractor和StateInjector（适配新结构）
- [ ] 添加对象序列化/反序列化支持
- [ ] 集成到现有Model和Result类

### 7.2 第二阶段：完整工作流程
- [ ] 实现项目级日循环模拟工作流程
- [ ] 添加项目汇总和时间序列管理
- [ ] 实现数据验证和错误处理
- [ ] 编写完整的使用文档和示例

### 7.3 第三阶段：数据同化准备
- [ ] 设计数据同化接口（基于新结构）
- [ ] 实现观测数据处理和同化变量存储
- [ ] 预留集合模拟支持
- [ ] 性能优化和大规模测试

### 7.4 第四阶段：高级功能
- [ ] 多项目管理和比较分析
- [ ] 数据导出和可视化工具
- [ ] 并行计算支持
- [ ] 云存储适配

## 8. 技术优势（更新版）

### 8.1 数据组织优势
1. **项目隔离**：不同地块和年份的数据完全分离，避免混淆
2. **日期结构化**：每日数据独立存储，便于随机访问和管理
3. **状态-参数分离**：清晰区分状态变量和参数变量，支持数据同化
4. **对象完整性**：直接存储Model和Result对象，保持数据完整性

### 8.2 性能优势
1. **高效存储**：HDF5二进制格式和压缩支持，显著节省空间
2. **快速访问**：层次化结构支持快速定位和读取特定日期数据
3. **批量操作**：支持批量读写和时间序列分析
4. **内存优化**：按需加载，支持大规模长期模拟

### 8.3 科学计算优势
1. **标准化格式**：HDF5是科学计算领域的标准格式
2. **元数据丰富**：支持详细的元数据存储和查询
3. **跨平台兼容**：支持多种编程语言和工具
4. **版本控制友好**：结构化数据便于版本管理

### 8.4 扩展性优势
1. **数据同化就绪**：预留完整的数据同化接口和存储结构
2. **多项目支持**：单文件支持多个项目，便于比较分析
3. **自定义变量**：灵活支持新的状态变量和参数变量
4. **集成友好**：不破坏现有架构，平滑集成

## 9. 与原有方案的对比

| 特性 | 原有方案 | 新方案（项目-日期结构） |
|------|----------|------------------------|
| 数据组织 | 时间序列为主 | 项目-日期层次结构 |
| 项目隔离 | 需要多个文件 | 单文件多项目支持 |
| 日数据访问 | 需要索引计算 | 直接按日期访问 |
| 对象存储 | 仅存储提取的数据 | 完整存储Model/Result对象 |
| 状态传递 | 需要额外逻辑 | 自然的日期间传递 |
| 数据同化 | 需要重构时间序列 | 直接在日期级别操作 |
| 可扩展性 | 受时间序列结构限制 | 高度灵活的层次结构 |

## 10. 总结与展望

### 10.1 方案总结

基于您建议的项目-日期分层结构，新的HDF5集成方案具有以下核心特点：

1. **清晰的数据组织**：project.h5 -> 项目组 -> 日期组 -> 输入/输出/状态-参数变量
2. **完整的对象存储**：直接序列化存储Model和Result对象，保持数据完整性
3. **灵活的状态管理**：状态变量和参数变量分离存储，支持数据同化
4. **项目级别汇总**：自动生成时间序列和统计信息，便于分析

### 10.2 实施建议

1. **优先实施**：先实现ProjectHDF5Manager核心类和基本的日循环工作流程
2. **渐进集成**：逐步集成到现有pyAHC架构中，保持向后兼容
3. **充分测试**：在小规模数据上充分测试后再应用到大规模模拟
4. **文档完善**：提供详细的使用文档和示例代码

### 10.3 未来发展

1. **数据同化算法**：基于新结构实现EnKF、3DVar等同化算法
2. **并行计算**：支持多项目并行模拟和分析
3. **云计算集成**：适配云存储和分布式计算环境
4. **机器学习**：结合深度学习进行模型校准和预测

这个更新的方案更好地满足了您的需求，为pyAHC项目的数据同化功能提供了科学、高效、可扩展的基础架构。

## 9. 详细技术实现

### 9.1 HDF5文件结构详细设计

#### 9.1.1 元数据组织
```python
# metadata组的详细结构
metadata = {
    'model_info': {
        'pyahc_version': '0.1.0',
        'ahc_version': 'V201',
        'model_type': 'agricultural_hydrological_crop',
        'creation_date': '2024-01-01T00:00:00Z',
        'creator': 'user_name',
        'description': 'Daily simulation with data assimilation support'
    },
    'simulation_period': {
        'start_date': '2013-05-01',
        'end_date': '2013-09-25',
        'time_step': 'daily',
        'total_days': 147
    },
    'spatial_info': {
        'location': 'hetao',
        'latitude': 45.75,
        'longitude': None,
        'altitude': 1039.3,
        'soil_layers': 30,
        'max_depth': 300.0
    }
}
```

#### 9.1.2 状态变量数据结构
```python
# states组的数据集定义
state_datasets = {
    'soil_moisture': {
        'shape': (n_days, n_layers),
        'dtype': 'float64',
        'units': 'cm3/cm3',
        'description': 'Volumetric soil moisture content by layer',
        'compression': 'gzip',
        'compression_opts': 6
    },
    'crop_lai': {
        'shape': (n_days,),
        'dtype': 'float64',
        'units': 'm2/m2',
        'description': 'Leaf Area Index',
        'compression': 'gzip'
    },
    'crop_biomass': {
        'shape': (n_days,),
        'dtype': 'float64',
        'units': 'kg/ha',
        'description': 'Above-ground biomass',
        'compression': 'gzip'
    },
    'root_depth': {
        'shape': (n_days,),
        'dtype': 'float64',
        'units': 'cm',
        'description': 'Effective rooting depth',
        'compression': 'gzip'
    }
}
```

### 9.2 核心类的详细实现

#### 9.2.1 HDF5Manager完整实现
```python
import h5py
import numpy as np
from pathlib import Path
from datetime import datetime, date
from typing import Dict, Any, Optional, Union
import logging

class HDF5Manager:
    """HDF5数据管理核心类 - 完整实现"""

    def __init__(self, filepath: Union[str, Path], mode: str = 'a'):
        self.filepath = Path(filepath)
        self.mode = mode
        self._file = None
        self.logger = logging.getLogger(__name__)

    def __enter__(self):
        self.open()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def open(self):
        """打开HDF5文件"""
        if self._file is None:
            self._file = h5py.File(self.filepath, self.mode)
            self.logger.info(f"Opened HDF5 file: {self.filepath}")

    def close(self):
        """关闭HDF5文件"""
        if self._file is not None:
            self._file.close()
            self._file = None
            self.logger.info("Closed HDF5 file")

    def initialize_structure(self, model: 'Model') -> None:
        """初始化HDF5文件结构"""
        self.open()

        # 创建主要组
        groups = ['metadata', 'parameters', 'states', 'inputs', 'outputs', 'assimilation']
        for group in groups:
            if group not in self._file:
                self._file.create_group(group)

        # 初始化元数据
        self._initialize_metadata(model)

        # 初始化状态变量数据集
        self._initialize_state_datasets(model)

        # 初始化参数存储
        self._initialize_parameters(model)

        self.logger.info("HDF5 structure initialized")

    def _initialize_metadata(self, model: 'Model') -> None:
        """初始化元数据"""
        metadata_group = self._file['metadata']

        # 模型信息
        model_info = metadata_group.create_group('model_info')
        model_info.attrs['pyahc_version'] = '0.1.0'
        model_info.attrs['ahc_version'] = 'V201'
        model_info.attrs['creation_date'] = datetime.now().isoformat()

        # 模拟时间段
        sim_period = metadata_group.create_group('simulation_period')
        if model.generalsettings:
            sim_period.attrs['start_date'] = model.generalsettings.tstart.isoformat()
            sim_period.attrs['end_date'] = model.generalsettings.tend.isoformat()

    def _initialize_state_datasets(self, model: 'Model') -> None:
        """初始化状态变量数据集"""
        states_group = self._file['states']

        # 计算模拟天数
        if model.generalsettings:
            start_date = model.generalsettings.tstart
            end_date = model.generalsettings.tend
            n_days = (end_date - start_date).days + 1
        else:
            n_days = 365  # 默认值

        # 土壤层数（从模型配置中获取）
        n_layers = 30  # 默认值，可以从模型中动态获取

        # 创建状态变量数据集
        state_configs = {
            'soil_moisture': (n_days, n_layers),
            'crop_lai': (n_days,),
            'crop_biomass': (n_days,),
            'root_depth': (n_days,),
            'water_stress': (n_days,)
        }

        for var_name, shape in state_configs.items():
            if var_name not in states_group:
                dataset = states_group.create_dataset(
                    var_name,
                    shape=shape,
                    dtype='float64',
                    compression='gzip',
                    compression_opts=6,
                    fillvalue=np.nan
                )
                dataset.attrs['units'] = self._get_variable_units(var_name)
                dataset.attrs['description'] = self._get_variable_description(var_name)

    def _get_variable_units(self, var_name: str) -> str:
        """获取变量单位"""
        units_map = {
            'soil_moisture': 'cm3/cm3',
            'crop_lai': 'm2/m2',
            'crop_biomass': 'kg/ha',
            'root_depth': 'cm',
            'water_stress': 'dimensionless'
        }
        return units_map.get(var_name, 'unknown')

    def _get_variable_description(self, var_name: str) -> str:
        """获取变量描述"""
        desc_map = {
            'soil_moisture': 'Volumetric soil moisture content by layer',
            'crop_lai': 'Leaf Area Index',
            'crop_biomass': 'Above-ground biomass',
            'root_depth': 'Effective rooting depth',
            'water_stress': 'Water stress factor'
        }
        return desc_map.get(var_name, 'No description')

    def _initialize_parameters(self, model: 'Model') -> None:
        """初始化参数存储"""
        params_group = self._file['parameters']

        # 存储作物参数
        if model.crop and hasattr(model.crop, 'cropfiles'):
            crop_group = params_group.create_group('crop')
            # 这里可以存储作物参数的序列化数据

        # 存储土壤参数
        if model.soilmoisture:
            soil_group = params_group.create_group('soil')
            # 存储土壤参数

    def save_model_state(self, date: Union[date, datetime], states: Dict[str, Any]) -> None:
        """保存模型状态到HDF5"""
        self.open()

        # 计算时间索引
        time_index = self._get_time_index(date)

        states_group = self._file['states']

        for var_name, value in states.items():
            if var_name in states_group:
                dataset = states_group[var_name]
                if len(dataset.shape) == 1:
                    # 1D数据（如LAI）
                    dataset[time_index] = value
                elif len(dataset.shape) == 2:
                    # 2D数据（如土壤含水量剖面）
                    dataset[time_index, :] = value

        self.logger.debug(f"Saved states for date {date}")

    def load_model_state(self, date: Union[date, datetime]) -> Dict[str, Any]:
        """从HDF5加载模型状态"""
        self.open()

        time_index = self._get_time_index(date)
        states_group = self._file['states']

        states = {}
        for var_name in states_group.keys():
            dataset = states_group[var_name]
            if len(dataset.shape) == 1:
                states[var_name] = dataset[time_index]
            elif len(dataset.shape) == 2:
                states[var_name] = dataset[time_index, :]

        self.logger.debug(f"Loaded states for date {date}")
        return states

    def _get_time_index(self, target_date: Union[date, datetime]) -> int:
        """获取日期对应的时间索引"""
        if isinstance(target_date, datetime):
            target_date = target_date.date()

        # 从元数据中获取开始日期
        start_date_str = self._file['metadata/simulation_period'].attrs['start_date']
        start_date = datetime.fromisoformat(start_date_str).date()

        time_index = (target_date - start_date).days
        return time_index

    def get_state_timeseries(self, variable: str,
                           start_date: Optional[date] = None,
                           end_date: Optional[date] = None) -> np.ndarray:
        """获取状态变量时间序列"""
        self.open()

        if variable not in self._file['states']:
            raise ValueError(f"Variable {variable} not found in states")

        dataset = self._file['states'][variable]

        if start_date is None and end_date is None:
            return dataset[:]

        # 计算时间索引范围
        start_idx = 0 if start_date is None else self._get_time_index(start_date)
        end_idx = dataset.shape[0] if end_date is None else self._get_time_index(end_date) + 1

        return dataset[start_idx:end_idx]

    def save_model_results(self, date: Union[date, datetime], result: 'Result') -> None:
        """保存模型结果"""
        self.open()

        time_index = self._get_time_index(date)
        outputs_group = self._file['outputs']

        # 保存CSV输出（如果存在）
        if hasattr(result, 'csv') and result.csv is not None:
            if 'daily' not in outputs_group:
                # 创建日输出数据集
                n_days = self._get_total_days()
                n_vars = len(result.csv.columns)
                daily_dataset = outputs_group.create_dataset(
                    'daily',
                    shape=(n_days, n_vars),
                    dtype='float64',
                    compression='gzip'
                )
                # 保存列名作为属性
                daily_dataset.attrs['columns'] = [col.encode('utf-8') for col in result.csv.columns]

            # 保存当日数据
            daily_data = result.csv.iloc[-1].values  # 获取最后一行（当日）数据
            outputs_group['daily'][time_index, :] = daily_data

        self.logger.debug(f"Saved results for date {date}")

    def _get_total_days(self) -> int:
        """获取总模拟天数"""
        start_date_str = self._file['metadata/simulation_period'].attrs['start_date']
        end_date_str = self._file['metadata/simulation_period'].attrs['end_date']

        start_date = datetime.fromisoformat(start_date_str).date()
        end_date = datetime.fromisoformat(end_date_str).date()

        return (end_date - start_date).days + 1
```

### 9.3 状态变量提取和注入的详细实现

#### 9.3.1 StateExtractor详细实现
```python
class StateExtractor:
    """从模型结果中提取状态变量的详细实现"""

    @staticmethod
    def extract_soil_moisture(result: 'Result') -> Optional[np.ndarray]:
        """从结果中提取土壤含水量剖面"""
        try:
            # 方法1：从CSV输出中提取
            if hasattr(result, 'csv') and result.csv is not None:
                # 查找土壤含水量相关列
                moisture_cols = [col for col in result.csv.columns
                               if 'moisture' in col.lower() or 'theta' in col.lower()]
                if moisture_cols:
                    return result.csv[moisture_cols].iloc[-1].values

            # 方法2：从ASCII输出中解析
            if hasattr(result, 'ascii') and result.ascii:
                # 解析特定格式的ASCII文件
                for file_ext, content in result.ascii.items():
                    if 'sba' in file_ext.lower():  # 土壤水分平衡文件
                        return StateExtractor._parse_soil_moisture_from_sba(content)

            return None

        except Exception as e:
            logging.warning(f"Failed to extract soil moisture: {e}")
            return None

    @staticmethod
    def _parse_soil_moisture_from_sba(sba_content: str) -> Optional[np.ndarray]:
        """从SBA文件内容中解析土壤含水量"""
        # 这里需要根据实际的SBA文件格式来解析
        # 示例实现（需要根据实际格式调整）
        lines = sba_content.strip().split('\n')
        moisture_values = []

        for line in lines:
            if line.startswith(' ') and 'moisture' in line.lower():
                # 解析含水量数据行
                parts = line.split()
                try:
                    moisture_values.extend([float(x) for x in parts if x.replace('.', '').isdigit()])
                except ValueError:
                    continue

        return np.array(moisture_values) if moisture_values else None

    @staticmethod
    def extract_crop_lai(result: 'Result') -> Optional[float]:
        """提取作物叶面积指数"""
        try:
            if hasattr(result, 'csv') and result.csv is not None:
                # 查找LAI相关列
                lai_cols = [col for col in result.csv.columns
                           if 'lai' in col.lower() or 'leaf_area' in col.lower()]
                if lai_cols:
                    return float(result.csv[lai_cols[0]].iloc[-1])

            # 从ASCII文件中提取
            if hasattr(result, 'ascii') and result.ascii:
                for file_ext, content in result.ascii.items():
                    if 'cr1' in file_ext.lower():  # 作物生长文件
                        return StateExtractor._parse_lai_from_cr1(content)

            return None

        except Exception as e:
            logging.warning(f"Failed to extract LAI: {e}")
            return None

    @staticmethod
    def _parse_lai_from_cr1(cr1_content: str) -> Optional[float]:
        """从CR1文件中解析LAI"""
        lines = cr1_content.strip().split('\n')

        for line in reversed(lines):  # 从后往前找最新数据
            if 'lai' in line.lower() or line.strip().startswith('LAI'):
                parts = line.split()
                for part in parts:
                    try:
                        return float(part)
                    except ValueError:
                        continue

        return None

    @staticmethod
    def extract_crop_biomass(result: 'Result') -> Optional[float]:
        """提取作物生物量"""
        try:
            if hasattr(result, 'csv') and result.csv is not None:
                biomass_cols = [col for col in result.csv.columns
                              if 'biomass' in col.lower() or 'dry_matter' in col.lower()]
                if biomass_cols:
                    return float(result.csv[biomass_cols[0]].iloc[-1])

            return None

        except Exception as e:
            logging.warning(f"Failed to extract biomass: {e}")
            return None

    @staticmethod
    def extract_all_states(result: 'Result') -> Dict[str, Any]:
        """提取所有状态变量"""
        states = {}

        # 提取各种状态变量
        soil_moisture = StateExtractor.extract_soil_moisture(result)
        if soil_moisture is not None:
            states['soil_moisture'] = soil_moisture

        lai = StateExtractor.extract_crop_lai(result)
        if lai is not None:
            states['crop_lai'] = lai

        biomass = StateExtractor.extract_crop_biomass(result)
        if biomass is not None:
            states['crop_biomass'] = biomass

        return states

#### 9.3.2 StateInjector详细实现
```python
class StateInjector:
    """将状态变量注入到模型输入中的详细实现"""

    @staticmethod
    def inject_soil_moisture(model: 'Model', moisture: np.ndarray) -> 'Model':
        """将土壤含水量注入到模型中"""
        try:
            # 创建模型副本以避免修改原模型
            updated_model = model.model_copy(deep=True)

            # 更新土壤含水量初始条件
            if updated_model.soilmoisture:
                # 将numpy数组转换为模型期望的格式
                if hasattr(updated_model.soilmoisture, 'thetai'):
                    # 更新初始含水量
                    updated_model.soilmoisture.thetai = moisture.tolist()

            return updated_model

        except Exception as e:
            logging.error(f"Failed to inject soil moisture: {e}")
            return model

    @staticmethod
    def inject_crop_state(model: 'Model', lai: Optional[float] = None,
                         biomass: Optional[float] = None) -> 'Model':
        """注入作物状态变量"""
        try:
            updated_model = model.model_copy(deep=True)

            # 注入LAI
            if lai is not None and updated_model.crop:
                # 这里需要根据具体的作物模型结构来更新
                # 可能需要更新作物发育阶段或直接设置LAI值
                pass

            # 注入生物量
            if biomass is not None:
                # 更新生物量相关参数
                pass

            return updated_model

        except Exception as e:
            logging.error(f"Failed to inject crop state: {e}")
            return model

    @staticmethod
    def inject_all_states(model: 'Model', states: Dict[str, Any]) -> 'Model':
        """注入所有状态变量"""
        updated_model = model

        # 注入土壤含水量
        if 'soil_moisture' in states:
            updated_model = StateInjector.inject_soil_moisture(
                updated_model, states['soil_moisture']
            )

        # 注入作物状态
        crop_states = {}
        if 'crop_lai' in states:
            crop_states['lai'] = states['crop_lai']
        if 'crop_biomass' in states:
            crop_states['biomass'] = states['crop_biomass']

        if crop_states:
            updated_model = StateInjector.inject_crop_state(
                updated_model, **crop_states
            )

        return updated_model
```

### 9.4 完整的日循环模拟工作流程

#### 9.4.1 主要工作流程函数
```python
def initialize_simulation(config: Dict[str, Any]) -> HDF5Manager:
    """初始化数据同化模拟"""

    # 创建HDF5管理器
    hdf5_path = config.get('hdf5_path', 'simulation.h5')
    hdf5_manager = HDF5Manager(hdf5_path)

    # 创建基础模型
    base_model = create_model_components()  # 使用现有函数

    # 根据配置调整模型参数
    if 'start_date' in config:
        base_model.generalsettings.tstart = config['start_date']
    if 'end_date' in config:
        base_model.generalsettings.tend = config['end_date']

    # 初始化HDF5结构
    hdf5_manager.initialize_structure(base_model)

    # 保存初始模型参数
    with hdf5_manager:
        params_group = hdf5_manager._file['parameters']
        # 序列化并保存模型参数
        model_dict = base_model.model_dump()
        _save_dict_to_hdf5(params_group, model_dict, 'initial_model')

    return hdf5_manager

def daily_simulation_step(hdf5_manager: HDF5Manager,
                         current_date: date,
                         base_model: 'Model') -> 'Result':
    """执行单日模拟步骤"""

    # 1. 准备当日模型
    daily_model = base_model.model_copy(deep=True)

    # 2. 加载前一日状态（如果存在）
    previous_date = current_date - timedelta(days=1)
    try:
        previous_states = hdf5_manager.load_model_state(previous_date)
        if previous_states:
            daily_model = StateInjector.inject_all_states(daily_model, previous_states)
            logging.info(f"Loaded states from {previous_date}")
    except Exception as e:
        logging.warning(f"Could not load previous states: {e}")

    # 3. 更新模拟日期
    daily_model.generalsettings.tstart = current_date
    daily_model.generalsettings.tend = current_date

    # 4. 运行模型
    try:
        result = daily_model.run()
        logging.info(f"Model run completed for {current_date}")
    except Exception as e:
        logging.error(f"Model run failed for {current_date}: {e}")
        raise

    # 5. 提取状态变量
    current_states = StateExtractor.extract_all_states(result)

    # 6. 保存状态和结果
    hdf5_manager.save_model_state(current_date, current_states)
    hdf5_manager.save_model_results(current_date, result)

    logging.info(f"Saved states and results for {current_date}")

    return result

def run_daily_simulation(config: Dict[str, Any]) -> HDF5Manager:
    """运行完整的日循环模拟"""

    # 初始化
    hdf5_manager = initialize_simulation(config)

    # 获取模拟参数
    start_date = config['start_date']
    end_date = config['end_date']

    # 创建基础模型
    base_model = create_model_components()

    # 日循环模拟
    current_date = start_date
    total_days = (end_date - start_date).days + 1

    logging.info(f"Starting daily simulation from {start_date} to {end_date} ({total_days} days)")

    with hdf5_manager:
        day_count = 0
        while current_date <= end_date:
            try:
                # 执行单日模拟
                result = daily_simulation_step(hdf5_manager, current_date, base_model)

                # 可选：检查观测数据同化
                if config.get('enable_assimilation', False):
                    observations = load_observations_for_date(current_date, config)
                    if observations:
                        assimilate_observations(hdf5_manager, observations, current_date)

                day_count += 1
                if day_count % 10 == 0:
                    logging.info(f"Completed {day_count}/{total_days} days")

            except Exception as e:
                logging.error(f"Simulation failed on {current_date}: {e}")
                if config.get('stop_on_error', True):
                    raise

            current_date += timedelta(days=1)

    logging.info("Daily simulation completed successfully")
    return hdf5_manager

def load_observations_for_date(target_date: date, config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """加载指定日期的观测数据（预留接口）"""
    # 这里是预留的观测数据加载接口
    # 实际实现需要根据观测数据的格式和来源
    obs_file = config.get('observations_file')
    if not obs_file:
        return None

    # 示例实现
    try:
        # 从文件或数据库加载观测数据
        # observations = load_from_file(obs_file, target_date)
        # return observations
        return None
    except Exception as e:
        logging.warning(f"Failed to load observations for {target_date}: {e}")
        return None

def assimilate_observations(hdf5_manager: HDF5Manager,
                          observations: Dict[str, Any],
                          date: date) -> None:
    """数据同化接口（预留实现）"""

    # 这是为未来数据同化功能预留的接口
    # 实际实现可能包括：
    # 1. 加载当前模型状态
    # 2. 应用同化算法（如EnKF、3DVar等）
    # 3. 更新状态变量
    # 4. 保存同化结果

    logging.info(f"Data assimilation interface called for {date}")

    # 示例框架
    try:
        # 1. 加载当前状态
        current_states = hdf5_manager.load_model_state(date)

        # 2. 应用同化算法（这里是占位符）
        # updated_states = apply_assimilation_algorithm(current_states, observations)

        # 3. 保存更新后的状态
        # hdf5_manager.save_model_state(date, updated_states)

        # 4. 记录同化信息
        with hdf5_manager:
            assim_group = hdf5_manager._file['assimilation']
            if 'log' not in assim_group:
                assim_group.create_group('log')

            # 记录同化事件
            log_entry = f"{date.isoformat()}: Assimilation applied"
            # 这里可以保存更详细的同化信息

    except Exception as e:
        logging.error(f"Data assimilation failed for {date}: {e}")

def _save_dict_to_hdf5(group: h5py.Group, data_dict: Dict[str, Any], name: str) -> None:
    """将字典保存到HDF5组中"""
    import json

    # 将字典序列化为JSON字符串
    json_str = json.dumps(data_dict, default=str, indent=2)

    # 保存为字符串数据集
    if name in group:
        del group[name]

    dataset = group.create_dataset(name, data=json_str.encode('utf-8'))
    dataset.attrs['type'] = 'json_dict'
    dataset.attrs['encoding'] = 'utf-8'
```

### 9.5 使用示例和最佳实践

#### 9.5.1 完整使用示例
```python
from datetime import date, timedelta
import logging
from pyahc.db.hdf5 import HDF5Manager, StateExtractor, StateInjector
from pyahc.model.model import Model

# 配置日志
logging.basicConfig(level=logging.INFO)

def main():
    """主函数 - 演示完整的HDF5集成使用"""

    # 1. 配置模拟参数
    config = {
        'hdf5_path': 'hetao_corn_simulation.h5',
        'start_date': date(2013, 5, 1),
        'end_date': date(2013, 5, 10),  # 短期测试
        'enable_assimilation': False,
        'stop_on_error': True
    }

    # 2. 运行日循环模拟
    hdf5_manager = run_daily_simulation(config)

    # 3. 分析结果
    analyze_simulation_results(hdf5_manager, config)

def analyze_simulation_results(hdf5_manager: HDF5Manager, config: Dict[str, Any]):
    """分析模拟结果"""

    start_date = config['start_date']
    end_date = config['end_date']

    print(f"\n=== 模拟结果分析 ===")
    print(f"模拟期间: {start_date} 到 {end_date}")

    with hdf5_manager:
        # 获取状态变量时间序列
        try:
            lai_series = hdf5_manager.get_state_timeseries('crop_lai', start_date, end_date)
            print(f"LAI变化范围: {lai_series.min():.3f} - {lai_series.max():.3f}")

            soil_moisture = hdf5_manager.get_state_timeseries('soil_moisture', start_date, end_date)
            if soil_moisture.size > 0:
                print(f"土壤含水量变化: {soil_moisture.mean():.3f} ± {soil_moisture.std():.3f}")

        except Exception as e:
            print(f"分析过程中出现错误: {e}")

        # 显示HDF5文件结构
        print(f"\nHDF5文件结构:")
        _print_hdf5_structure(hdf5_manager._file, indent=0)

def _print_hdf5_structure(group, indent=0):
    """递归打印HDF5文件结构"""
    prefix = "  " * indent

    for key in group.keys():
        item = group[key]
        if isinstance(item, h5py.Group):
            print(f"{prefix}{key}/")
            _print_hdf5_structure(item, indent + 1)
        else:
            print(f"{prefix}{key} {item.shape} {item.dtype}")

if __name__ == "__main__":
    main()
```

#### 9.5.2 高级功能示例
```python
def advanced_usage_example():
    """高级功能使用示例"""

    # 1. 自定义状态变量提取
    class CustomStateExtractor(StateExtractor):
        @staticmethod
        def extract_water_stress(result: 'Result') -> Optional[float]:
            """提取水分胁迫指数"""
            # 自定义提取逻辑
            return None

    # 2. 批量分析多个模拟
    def batch_analysis(simulation_files: List[str]):
        """批量分析多个模拟文件"""
        results = {}

        for file_path in simulation_files:
            with HDF5Manager(file_path, mode='r') as manager:
                lai_data = manager.get_state_timeseries('crop_lai')
                results[file_path] = {
                    'max_lai': lai_data.max(),
                    'mean_lai': lai_data.mean(),
                    'final_lai': lai_data[-1]
                }

        return results

    # 3. 状态变量可视化
    def visualize_states(hdf5_manager: HDF5Manager):
        """可视化状态变量"""
        import matplotlib.pyplot as plt

        with hdf5_manager:
            # 获取数据
            lai_data = hdf5_manager.get_state_timeseries('crop_lai')
            moisture_data = hdf5_manager.get_state_timeseries('soil_moisture')

            # 创建图表
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

            # LAI时间序列
            ax1.plot(lai_data)
            ax1.set_title('Crop LAI Evolution')
            ax1.set_ylabel('LAI (m²/m²)')

            # 土壤含水量热图
            if moisture_data.size > 0:
                im = ax2.imshow(moisture_data.T, aspect='auto', cmap='Blues')
                ax2.set_title('Soil Moisture Profile')
                ax2.set_ylabel('Soil Layer')
                ax2.set_xlabel('Time (days)')
                plt.colorbar(im, ax=ax2, label='Moisture Content')

            plt.tight_layout()
            plt.show()

def performance_optimization_example():
    """性能优化示例"""

    # 1. 批量写入优化
    def batch_save_states(hdf5_manager: HDF5Manager,
                         states_batch: Dict[date, Dict[str, Any]]):
        """批量保存状态以提高性能"""
        with hdf5_manager:
            for date_key, states in states_batch.items():
                hdf5_manager.save_model_state(date_key, states)

    # 2. 内存优化的大规模模拟
    def memory_efficient_simulation(config: Dict[str, Any]):
        """内存优化的大规模模拟"""
        hdf5_manager = initialize_simulation(config)

        # 分批处理以减少内存使用
        batch_size = config.get('batch_size', 30)  # 30天一批

        start_date = config['start_date']
        end_date = config['end_date']

        current_start = start_date
        while current_start <= end_date:
            current_end = min(current_start + timedelta(days=batch_size-1), end_date)

            # 处理当前批次
            batch_config = config.copy()
            batch_config['start_date'] = current_start
            batch_config['end_date'] = current_end

            process_simulation_batch(hdf5_manager, batch_config)

            current_start = current_end + timedelta(days=1)

        return hdf5_manager
```

## 10. 总结与展望

### 10.1 方案总结

本HDF5集成方案为pyAHC项目提供了：

1. **完整的数据管理架构**：基于HDF5的层次化数据存储，支持模型状态、参数和结果的统一管理
2. **高效的状态传递机制**：实现了日循环模拟中状态变量的自动提取、存储和注入
3. **可扩展的数据同化接口**：为未来的数据同化功能预留了完整的接口和架构
4. **科学的数据组织方式**：遵循科学计算最佳实践，便于数据分析和可视化

### 10.2 技术优势

- **性能优化**：HDF5的二进制格式和压缩支持，显著提高数据读写效率
- **标准化**：采用科学计算领域广泛认可的HDF5格式
- **可维护性**：模块化设计，易于扩展和维护
- **兼容性**：不破坏现有架构，平滑集成

### 10.3 未来发展方向

1. **数据同化算法集成**：实现EnKF、3DVar等同化算法
2. **并行计算支持**：支持多进程/多线程的大规模模拟
3. **云计算适配**：支持云存储和分布式计算
4. **可视化增强**：开发专门的数据可视化工具
5. **机器学习集成**：结合深度学习进行模型校准和预测

这个方案为pyAHC项目的数据同化功能奠定了坚实的技术基础，既满足当前需求，又为未来发展提供了充分的扩展空间。
```

## 7. 实施计划

### 7.1 第一阶段：基础HDF5支持
- [ ] 实现HDF5Manager核心类
- [ ] 实现StateExtractor和StateInjector
- [ ] 添加基本的状态变量支持
- [ ] 集成到现有Model和Result类

### 7.2 第二阶段：完整工作流程
- [ ] 实现日循环模拟工作流程
- [ ] 添加状态变量时间序列管理
- [ ] 实现数据验证和错误处理
- [ ] 编写完整的使用文档

### 7.3 第三阶段：数据同化准备
- [ ] 设计数据同化接口
- [ ] 实现观测数据处理
- [ ] 预留集合模拟支持
- [ ] 性能优化和测试

## 8. 技术优势

1. **科学性**：基于HDF5的层次化数据结构，符合科学计算标准
2. **可扩展性**：模块化设计，易于添加新的状态变量和功能
3. **性能**：高效的二进制存储，支持大规模时间序列数据
4. **兼容性**：不破坏现有架构，保持向后兼容
5. **标准化**：遵循科学数据管理最佳实践

这个方案为pyAHC项目提供了坚实的数据同化基础架构，既满足当前的日循环模拟需求，又为未来的数据同化功能扩展奠定了基础。
